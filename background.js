// Store active summarization tasks
const activeTasks = new Map();

// Listen for messages from the popup
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
    if (request.type === 'summarize') {
        // Generate a unique task ID
        const taskId = Date.now().toString();

        // Store the original content in case we need to fall back
        activeTasks.set(taskId, {
            originalContent: request.content,
            apiKey: request.apiKey,
            model: request.model,
            status: 'starting'
        });

        // Immediately respond with the task ID so popup knows to start polling
        sendResponse({ taskId: taskId });

        // Start the processing pipeline
        processSummarizationTask(taskId);

        return true;
    }

    // Handle status check requests from the popup
    if (request.type === 'checkStatus') {
        const taskId = request.taskId;
        const task = activeTasks.get(taskId);

        if (!task) {
            sendResponse({ error: 'Task not found' });
            return true;
        }

        // Return the current status of the task
        sendResponse(task);

        // Clean up completed tasks after a while to prevent memory leaks
        if (task.status === 'complete' || task.status === 'error') {
            setTimeout(() => {
                activeTasks.delete(taskId);
            }, 60000); // Remove after 1 minute
        }

        return true;
    }
});

/**
 * Process a summarization task asynchronously
 * @param {string} taskId - The unique ID of the task
 */
async function processSummarizationTask(taskId) {
    const task = activeTasks.get(taskId);
    if (!task) return;

    try {
        // Update status to processing
        activeTasks.set(taskId, {
            ...task,
            status: 'processing',
            message: 'Processing text through API...'
        });

        // Step 1: Process text through the API
        let processedText;
        try {
            processedText = await processTextWithAPI(task.originalContent);

            // Update status after text processing
            activeTasks.set(taskId, {
                ...task,
                status: 'summarizing',
                message: 'Text processed successfully. Generating summary...',
                processedText
            });
        } catch (apiError) {
            console.warn('API processing failed:', apiError);

            // Update status to indicate fallback
            activeTasks.set(taskId, {
                ...task,
                status: 'fallback',
                message: 'API processing failed. Using direct summarization...',
                error: apiError.message
            });

            // Use original content as fallback
            processedText = task.originalContent;
        }

        // Step 2: Generate summary with Groq
        const summary = await summarizeWithGroq(processedText, task.apiKey, task.model);

        // Step 3: Update task with the completed summary
        if (summary.length > 5000) {
            // For long summaries, provide both partial and full versions
            const firstPart = summary.substring(0, 2000);

            activeTasks.set(taskId, {
                ...task,
                status: 'complete',
                summary: summary,
                partialSummary: firstPart,
                isLong: true,
                fallback: processedText === task.originalContent
            });
        } else {
            activeTasks.set(taskId, {
                ...task,
                status: 'complete',
                summary: summary,
                fallback: processedText === task.originalContent
            });
        }
    } catch (error) {
        console.error('Error in summarization task:', error);

        // Update task with error
        activeTasks.set(taskId, {
            ...task,
            status: 'error',
            error: error.message
        });
    }
}

/**
 * Process text through the API endpoint before sending it to AI for summarization
 * @param {string} text - The original webpage content
 * @returns {Promise<string>} - The processed text
 */
async function processTextWithAPI(text) {
    const endpoint = 'http://66.179.190.152:8081/api/process-async';
    try {
        const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                text: text
            })
        });

        if (!response.ok) {
            const errorMessages = {
                400: 'Bad Request: Please check your text format and try again.',
                404: 'Not Found: The API endpoint could not be found.',
                413: 'Request Entity Too Large: The text is too long. Please try with shorter content.',
                500: 'Internal Server Error: API servers are experiencing issues. Please try again later.',
                502: 'Bad Gateway: Temporary connection issue. Please try again.',
                503: 'Service Unavailable: API service is temporarily down. Please try again later.'
            };

            const errorMessage = errorMessages[response.status] || `API Error: ${response.status}`;
            throw new Error(errorMessage);
        }

        const data = await response.json();

        // Return the cleaned text from the API response
        return data.cleaned_text || text; // Fallback to original text if cleaned_text is not available
    } catch (error) {
        console.error('Error processing text with API:', error);
        // If there's an error with the API, we'll just use the original text
        // This ensures the extension still works even if the API is down
        return text;
    }
}

/**
 * Get the maximum output tokens for a specific model
 * @param {string} model - The model name
 * @returns {number} - The maximum output tokens for the model
 */
function getMaxTokensForModel(model) {
    const modelTokenLimits = {
        'meta-llama/llama-4-maverick-17b-128e-instruct': 8192,
        'openai/gpt-oss-120b': 32766,
        'moonshotai/kimi-k2-instruct': 16384,
        'llama-3.3-70b-versatile': 32768 // Default for existing model
    };

    // Return the specific limit for the model, or default to 32768 if model not found
    return modelTokenLimits[model] || 32768;
}

/**
 * Summarize the processed text using Groq API
 * @param {string} content - The processed text content
 * @param {string} apiKey - The Groq API key
 * @param {string} model - The model to use for summarization
 * @returns {Promise<string>} - The summary
 */
async function summarizeWithGroq(content, apiKey, model) {
    const endpoint = 'https://api.groq.com/openai/v1/chat/completions';

    // Use the provided model or fallback to default if not specified
    const selectedModel = model || 'llama-3.3-70b-versatile';

    try {
        const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${apiKey.trim()}`,
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                model: selectedModel,
                messages: [
                    {
                        role: "system",
                        content: "You are a helpful assistant that generates concise and accurate summaries of articles. Focus on the main points, key arguments, and important details from the article. Ignore any irrelevant content that might still be present in the text. Create a well-structured summary that captures the essence of the article."
                    },
                    {
                        role: "user",
                        content: `Please summarize the following article:\n\n${content}`
                    }
                ],
                temperature: 0.9,
                max_tokens: getMaxTokensForModel(selectedModel)
            })
        });

        if (!response.ok) {
            const errorMessages = {
                400: 'Bad Request: Please check your request format and try again.',
                401: 'Unauthorized: Invalid API key. Please verify your Groq API key in settings.',
                404: 'Not Found: The Groq API endpoint could not be found.',
                413: 'Request Entity Too Large: The request data is too long. Please try with shorter content.',
                422: 'Unprocessable Entity: The request data was invalid. Please try with different content.',
                429: 'Rate Limit Exceeded: Too many requests. Please wait a moment before trying again.',
                500: 'Internal Server Error: Groq servers are experiencing issues. Please try again later.',
                502: 'Bad Gateway: Temporary connection issue. Please try again.',
                503: 'Service Unavailable: Groq service is temporarily down. Please try again later.'
            };

            const errorMessage = errorMessages[response.status] || `Error: ${response.status}`;
            throw new Error(errorMessage);
        }

        const data = await response.json();
        return data.choices[0].message.content;
    } catch (error) {
        if (error.message.includes('Failed to fetch')) {
            throw new Error('Connection Error: Please check your internet connection and try again.');
        }
        throw error;
    }
}