document.addEventListener('DOMContentLoaded', () => {
    const container = document.getElementById('summaries-container');
    const clearAllButton = document.getElementById('clear-all');
    const searchInput = document.getElementById('search');
    const sortSelect = document.getElementById('sort');
    const paginationContainer = document.getElementById('pagination');
    const themeToggle = document.getElementById('theme-toggle');

    const ITEMS_PER_PAGE = 5;
    let currentPage = 1;
    let filteredSummaries = [];

    // Dark mode toggle
    themeToggle.addEventListener('click', () => {
        const html = document.documentElement;
        const currentTheme = html.getAttribute('data-theme');
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        html.setAttribute('data-theme', newTheme);
        chrome.storage.sync.set({ theme: newTheme });
    });

    // Load saved theme
    chrome.storage.sync.get('theme', (data) => {
        if (data.theme) {
            document.documentElement.setAttribute('data-theme', data.theme);
        }
    });

    function formatDate(dateString) {
        return new Date(dateString).toLocaleString();
    }

    function deleteSummary(index) {
        chrome.storage.sync.get('savedSummaries', (data) => {
            const savedSummaries = data.savedSummaries || [];
            savedSummaries.splice(index, 1);
            chrome.storage.sync.set({ savedSummaries }, loadSummaries);
        });
    }

    function clearAllSummaries() {
        if (confirm('Are you sure you want to delete all saved summaries?')) {
            chrome.storage.sync.set({ savedSummaries: [] }, loadSummaries);
        }
    }

    function filterSummaries(summaries, searchTerm) {
        return summaries.filter(summary => 
            summary.url.toLowerCase().includes(searchTerm.toLowerCase()) ||
            summary.summary.toLowerCase().includes(searchTerm.toLowerCase())
        );
    }

    function sortSummaries(summaries, sortMethod) {
        return [...summaries].sort((a, b) => {
            switch (sortMethod) {
                case 'date-asc':
                    return new Date(a.date) - new Date(b.date);
                case 'date-desc':
                    return new Date(b.date) - new Date(a.date);
                case 'url':
                    return a.url.localeCompare(b.url);
                default:
                    return 0;
            }
        });
    }

    function createPagination(totalItems) {
        const totalPages = Math.ceil(totalItems / ITEMS_PER_PAGE);
        paginationContainer.innerHTML = '';
        
        for (let i = 1; i <= totalPages; i++) {
            const button = document.createElement('button');
            button.className = `page-btn ${i === currentPage ? 'active' : ''}`;
            button.textContent = i;
            button.addEventListener('click', () => {
                currentPage = i;
                displaySummaries();
            });
            paginationContainer.appendChild(button);
        }
    }

    function displaySummaries() {
        const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
        const pageItems = filteredSummaries.slice(startIndex, startIndex + ITEMS_PER_PAGE);
        
        container.innerHTML = pageItems.length === 0 ? 
            '<div class="no-summaries">No matching summaries found</div>' : '';

        pageItems.forEach(summary => {
            const card = document.createElement('div');
            card.className = 'summary-card';
            card.innerHTML = `
                <div class="summary-date">${formatDate(summary.date)}</div>
                <a href="${summary.url}" target="_blank" class="summary-url">${summary.url}</a>
                <button class="delete-btn" data-url="${summary.url}">Delete</button>
                <div class="summary-text">${summary.summary}</div>
            `;
            container.appendChild(card);
        });

        // Add event listeners for delete buttons
        document.querySelectorAll('.delete-btn').forEach(button => {
            button.addEventListener('click', (e) => {
                const url = e.target.dataset.url;
                const index = filteredSummaries.findIndex(s => s.url === url);
                deleteSummary(index);
            });
        });
    }

    function loadSummaries() {
        chrome.storage.sync.get('savedSummaries', (data) => {
            const savedSummaries = data.savedSummaries || [];
            const searchTerm = searchInput.value;
            const sortMethod = sortSelect.value;

            filteredSummaries = sortSummaries(
                filterSummaries(savedSummaries, searchTerm),
                sortMethod
            );

            createPagination(filteredSummaries.length);
            displaySummaries();
        });
    }

    function downloadFile(content, filename, type) {
        const blob = new Blob([content], { type });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }

    function exportAsJson() {
        chrome.storage.sync.get('savedSummaries', (data) => {
            const savedSummaries = data.savedSummaries || [];
            const content = JSON.stringify(savedSummaries, null, 2);
            const date = new Date().toISOString().split('T')[0];
            downloadFile(content, `summaries-${date}.json`, 'application/json');
        });
    }

    function exportAsText() {
        chrome.storage.sync.get('savedSummaries', (data) => {
            const savedSummaries = data.savedSummaries || [];
            let content = '';
            
            savedSummaries.forEach((summary, index) => {
                content += `URL: ${summary.url}\n`;
                content += `Date: ${new Date(summary.date).toLocaleString()}\n`;
                content += `Summary: ${summary.summary}\n`;
                content += '='.repeat(80) + '\n\n';
            });
            
            const date = new Date().toISOString().split('T')[0];
            downloadFile(content, `summaries-${date}.txt`, 'text/plain');
        });
    }

    // Event listeners
    searchInput.addEventListener('input', () => {
        currentPage = 1;
        loadSummaries();
    });

    sortSelect.addEventListener('change', loadSummaries);
    clearAllButton.addEventListener('click', clearAllSummaries);
    document.getElementById('export-json').addEventListener('click', exportAsJson);
    document.getElementById('export-txt').addEventListener('click', exportAsText);

    loadSummaries();
});