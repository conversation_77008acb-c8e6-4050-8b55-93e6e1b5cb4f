<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Saved Summaries</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Add Nebula Sans font -->
    <style>
        @font-face {
            font-family: 'Nebula Sans';
            src: url('https://cdn.jsdelivr.net/gh/selfhoodstudios/nebula-font/NebulaSans-Regular.woff2') format('woff2'),
                 url('https://cdn.jsdelivr.net/gh/selfhoodstudios/nebula-font/NebulaSans-Regular.woff') format('woff');
            font-weight: normal;
            font-style: normal;
            font-display: swap;
        }

        @font-face {
            font-family: 'Nebula Sans';
            src: url('https://cdn.jsdelivr.net/gh/selfhoodstudios/nebula-font/NebulaSans-Bold.woff2') format('woff2'),
                 url('https://cdn.jsdelivr.net/gh/selfhoodstudios/nebula-font/NebulaSans-Bold.woff') format('woff');
            font-weight: bold;
            font-style: normal;
            font-display: swap;
        }
    </style>
    <style>
        :root {
            --lavender-primary: #7c3aed;    /* Main lavender */
            --lavender-dark: #6d28d9;       /* Darker shade for hover */
            --lavender-light: #ede9fe;      /* Light background */
            --lavender-medium: #a78bfa;     /* Medium shade */
            --lavender-border: #c4b5fd;     /* Border color */
            --text-primary: #2d1b69;        /* Dark text */
            --text-secondary: #6b4faa;      /* Secondary text */
            --background-primary: #ffffff;
            --background-secondary: #f8f8f8;
        }

        [data-theme="dark"] {
            --lavender-primary: #9d6dff;
            --lavender-dark: #8257fe;
            --lavender-light: #2d1b69;
            --lavender-medium: #7c3aed;
            --lavender-border: #3d2a8a;
            --text-primary: #e2e2e2;
            --text-secondary: #c4b5fd;
            --background-primary: #1a1625;
            --background-secondary: #231c35;
        }

        body {
            max-width: 1000px;
            margin: 0 auto;
            font-family: 'Nunito', system-ui, -apple-system, sans-serif;
            background-color: var(--background-primary);
            color: var(--text-primary);
            padding: 40px 20px;
            line-height: 1.6;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid var(--lavender-border);
        }

        h1 {
            font-size: 32px;
            margin: 0;
            background: linear-gradient(135deg, var(--lavender-primary), var(--lavender-dark));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: 700;
        }

        .header-buttons {
            display: flex;
            gap: 12px;
        }

        .export-btn, .clear-all-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .export-btn {
            background-color: var(--lavender-primary);
            color: white;
        }

        .export-btn:hover {
            background-color: var(--lavender-dark);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(124, 58, 237, 0.2);
        }

        .clear-all-btn {
            background-color: var(--lavender-light);
            color: var(--text-secondary);
        }

        .clear-all-btn:hover {
            background-color: var(--lavender-light);
            color: var(--lavender-primary);
        }

        .summary-card {
            background-color: var(--background-primary);
            border: 1px solid var(--lavender-border);
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            transition: all 0.2s ease;
            display: flex;
            flex-direction: column;
        }

        .summary-card:hover {
            border-color: var(--lavender-primary);
            box-shadow: 0 4px 12px rgba(124, 58, 237, 0.1);
            transform: translateY(-2px);
        }

        .summary-date {
            color: var(--text-secondary);
            font-size: 14px;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .summary-url {
            color: var(--lavender-primary);
            text-decoration: none;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
            display: block;
            word-break: break-all;
        }

        .summary-url:hover {
            text-decoration: underline;
        }

        .summary-text {
            font-family: 'Nebula Sans', 'Nunito', sans-serif;
            color: var(--text-primary);
            line-height: 1.6;
            margin-top: 16px;
            margin-bottom: 16px;
            font-size: 14px;
            letter-spacing: -0.01em;
        }

        .delete-btn {
            align-self: flex-end;
            padding: 8px 16px;
            background-color: var(--background-secondary);
            color: var(--text-secondary);
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .delete-btn:hover {
            background-color: #dc2626;
            color: white;
            transform: translateY(-1px);
        }

        .no-summaries {
            text-align: center;
            color: var(--text-secondary);
            margin-top: 100px;
            font-size: 18px;
            font-weight: 500;
            padding: 40px;
            background-color: var(--lavender-light);
            border-radius: 12px;
            border: 1px dashed var(--lavender-border);
        }

        .controls {
            display: flex;
            gap: 16px;
            margin-bottom: 24px;
            flex-wrap: wrap;
        }

        .search-box {
            flex: 1;
            min-width: 200px;
            padding: 10px 16px;
            border: 1px solid var(--lavender-border);
            border-radius: 8px;
            font-size: 14px;
            background-color: var(--background-primary);
            color: var(--text-primary);
        }

        .sort-select {
            padding: 10px 16px;
            border: 1px solid var(--lavender-border);
            border-radius: 8px;
            font-size: 14px;
            background-color: var(--background-primary);
            color: var(--text-primary);
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 8px;
            margin-top: 24px;
        }

        .page-btn {
            padding: 8px 16px;
            border: 1px solid var(--lavender-border);
            border-radius: 6px;
            background-color: var(--background-primary);
            color: var(--text-primary);
            cursor: pointer;
        }

        .page-btn.active {
            background-color: var(--lavender-primary);
            color: white;
            border-color: var(--lavender-primary);
        }

        .theme-toggle {
            padding: 10px;
            border-radius: 50%;
            border: 1px solid var(--lavender-border);
            background-color: var(--background-primary);
            color: var(--text-primary);
            cursor: pointer;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        @media (max-width: 768px) {
            body {
                padding: 20px 15px;
            }

            .header {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .header-buttons {
                flex-wrap: wrap;
                justify-content: center;
            }

            .summary-card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Saved Summaries</h1>
        <div class="header-buttons">
            <button id="theme-toggle" class="theme-toggle">🌓</button>
            <button id="export-json" class="export-btn">Export as JSON</button>
            <button id="export-txt" class="export-btn">Export as Text</button>
            <button id="clear-all" class="clear-all-btn">Clear All</button>
        </div>
    </div>
    <div class="controls">
        <input type="text" id="search" class="search-box" placeholder="Search summaries...">
        <select id="sort" class="sort-select">
            <option value="date-desc">Newest First</option>
            <option value="date-asc">Oldest First</option>
            <option value="url">URL</option>
        </select>
    </div>
    <div id="summaries-container"></div>
    <div id="pagination" class="pagination"></div>
    <script src="saved.js"></script>
</body>
</html>
