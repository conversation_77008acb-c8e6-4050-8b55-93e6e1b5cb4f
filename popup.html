<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Summarizer</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">

    <!-- Add Nebula Sans font -->
    <style>
        @font-face {
            font-family: 'Nebula Sans';
            src: url('https://cdn.jsdelivr.net/gh/selfhoodstudios/nebula-font/NebulaSans-Regular.woff2') format('woff2'),
                 url('https://cdn.jsdelivr.net/gh/selfhoodstudios/nebula-font/NebulaSans-Regular.woff') format('woff');
            font-weight: normal;
            font-style: normal;
            font-display: swap;
        }

        @font-face {
            font-family: 'Nebula Sans';
            src: url('https://cdn.jsdelivr.net/gh/selfhoodstudios/nebula-font/NebulaSans-Bold.woff2') format('woff2'),
                 url('https://cdn.jsdelivr.net/gh/selfhoodstudios/nebula-font/NebulaSans-Bold.woff') format('woff');
            font-weight: bold;
            font-style: normal;
            font-display: swap;
        }
    </style>
    <style>
        :root {
            --lavender-primary: #7c3aed;    /* Main lavender */
            --lavender-dark: #6d28d9;       /* Darker shade for hover */
            --lavender-light: #ede9fe;      /* Light background */
            --lavender-medium: #a78bfa;     /* Medium shade */
            --lavender-border: #c4b5fd;     /* Border color */
            --text-primary: #2d1b69;        /* Dark text */
            --text-secondary: #6b4faa;      /* Secondary text */
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.02); }
            100% { transform: scale(1); }
        }

        body {
            width: 400px;
            font-family: 'Inter', system-ui, -apple-system, sans-serif;
            background-color: #ffffff;
            color: var(--text-primary);
            padding: 0;
            margin: 0;
        }

        .header {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px 24px;
            border-bottom: 1px solid var(--lavender-border);
            background: #ffffff;
            animation: fadeIn 0.6s ease-out;
        }

        .logo {
            width: 28px;
            height: 28px;
        }

        h1 {
            font-size: 20px;
            margin: 0;
            background: linear-gradient(135deg, var(--lavender-primary), var(--lavender-dark));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: 600;
        }

        .tabs {
            display: flex;
            border-bottom: 1px solid var(--lavender-border);
            background: var(--lavender-light);
            padding: 0 24px;
            animation: fadeIn 0.6s ease-out 0.2s both;
        }

        .tab {
            padding: 12px 20px;
            font-size: 14px;
            font-weight: 500;
            color: var(--text-secondary);
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
            user-select: none;
        }

        .tab:hover {
            color: var(--lavender-primary);
        }

        .tab.active {
            color: var(--lavender-primary);
            border-bottom-color: var(--lavender-primary);
        }

        .tab-content {
            display: none;
            padding: 24px;
            animation: slideIn 0.5s ease-out;
        }

        .tab-content.active {
            display: block;
        }

        /* Settings Tab */
        .api-key-section {
            background-color: var(--lavender-light);
            padding: 16px;
            border-radius: 12px;
            border: 1px solid var(--lavender-border);
        }

        .api-key-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: var(--text-secondary);
            margin-bottom: 8px;
        }

        #apiKey {
            width: 100%;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            font-family: 'Inter', sans-serif;
            background-color: white;
            transition: all 0.2s ease;
            box-sizing: border-box;
        }

        #apiKey:focus {
            outline: none;
            border-color: var(--lavender-primary);
            box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
        }

        .model-select {
            margin-top: 16px;
            width: 100%;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            font-family: 'Inter', sans-serif;
            background-color: white;
            transition: all 0.2s ease;
            box-sizing: border-box;
            cursor: pointer;
        }

        .model-select:focus {
            outline: none;
            border-color: var(--lavender-primary);
            box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
        }

        /* Summary Tab */
        .summarize-btn {
            width: 100%;
            padding: 12px;
            background-color: var(--lavender-primary);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin-bottom: 16px;
            animation: pulse 2s infinite;
        }

        .summarize-btn:hover {
            background-color: var(--lavender-dark);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(124, 58, 237, 0.2);
            animation: none;
        }

        #summary {
            font-family: 'Nebula Sans', 'Inter', sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #374151;
            background-color: #ffffff;
            border: 1px solid var(--lavender-border);
            border-radius: 12px;
            padding: 20px;
            max-height: 300px;
            overflow-y: auto;
            position: relative;
            transition: all 0.3s ease-out;
            animation: slideIn 0.5s ease-out;
            letter-spacing: -0.01em;
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            color: #6b7280;
            font-size: 14px;
            gap: 12px;
            font-family: 'Nebula Sans', 'Inter', sans-serif;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 3px solid var(--lavender-light);
            border-top: 3px solid var(--lavender-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .error-message {
            background-color: #fde8e8;
            color: #991b1b;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 12px;
            animation: slideIn 0.3s ease-out;
        }

        .success-message {
            background-color: #e8fde8;
            color: #1b991b;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 12px;
            animation: slideIn 0.3s ease-out;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Improved scrollbar styling */
        #summary::-webkit-scrollbar {
            width: 8px;
        }

        #summary::-webkit-scrollbar-track {
            background: var(--lavender-light);
            border-radius: 4px;
        }

        #summary::-webkit-scrollbar-thumb {
            background: var(--lavender-medium);
            border-radius: 4px;
            border: 2px solid var(--lavender-light);
        }

        #summary::-webkit-scrollbar-thumb:hover {
            background: var(--lavender-primary);
        }

        /* Fade effect for text overflow */
        #summary::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 40px;
            background: linear-gradient(transparent, #ffffff);
            pointer-events: none;
            opacity: 0.8;
            border-bottom-left-radius: 12px;
            border-bottom-right-radius: 12px;
        }

        .button-group {
            display: flex;
            gap: 12px;
            margin-top: 16px;
        }

        .primary-button {
            width: 100%;
            padding: 12px;
            background-color: var(--lavender-primary);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .primary-button:hover {
            background-color: var(--lavender-dark);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(124, 58, 237, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(124, 58, 237, 0.25);
        }

        .primary-button:disabled {
            background-color: var(--lavender-medium);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .secondary-button {
            flex: 1;
            padding: 12px;
            background-color: var(--lavender-light);
            color: var(--text-secondary);
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .secondary-button:hover {
            background-color: #f3eeff;
            color: var(--lavender-primary);
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(124, 58, 237, 0.25);
        }

        .view-saved-link {
            display: block;
            text-align: center;
            margin-top: 16px;
            padding: 12px;
            color: var(--lavender-primary);
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            border: 1px solid var(--lavender-border);
            border-radius: 8px;
            transition: all 0.2s ease;
            animation: fadeIn 0.6s ease-out 0.4s both;
        }

        .view-saved-link:hover {
            background-color: var(--lavender-light);
            border-color: var(--lavender-primary);
            transform: translateY(-1px);
        }

        .dark-mode {
            background-color: #1a1a1a;
            color: #ffffff;
        }

        .dark-mode .tab-content {
            background-color: #2d2d2d;
        }

        .theme-toggle {
            position: absolute;
            top: 16px;
            right: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 40px;
            height: 20px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--lavender-primary);
        }

        input:checked + .slider:before {
            transform: translateX(20px);
        }

        .summary-stats {
            font-size: 12px;
            color: var(--text-secondary);
            margin-bottom: 8px;
        }



        .select-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            font-family: 'Inter', sans-serif;
            background-color: white;
            margin-bottom: 16px;
            transition: all 0.2s ease;
        }

        .select-input:focus {
            outline: none;
            border-color: var(--lavender-primary);
            box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
        }

        .provider-setting {
            margin-top: 16px;
        }

        /* Add these new styles for settings tab */
        .settings-container {
            background: linear-gradient(145deg, #f5f3ff, #ede9fe);
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(124, 58, 237, 0.1);
            animation: fadeIn 0.5s ease-out;
            width: 100%;
            box-sizing: border-box;
        }

        /* Update the API key input styles */
        .api-key-input {
            position: relative;
            margin-bottom: 24px;
            width: 100%;
            box-sizing: border-box;
        }

        .api-key-input input {
            width: 100%;
            padding: 12px 40px 12px 16px;
            border: 2px solid var(--lavender-border);
            border-radius: 12px;
            font-size: 14px;
            font-family: 'Inter', sans-serif;
            background-color: white;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .settings-header {
            margin-bottom: 24px;
            text-align: center;
        }

        .settings-header h2 {
            color: var(--lavender-primary);
            font-size: 18px;
            margin: 0 0 8px 0;
            font-weight: 600;
        }

        .settings-header p {
            color: var(--text-secondary);
            font-size: 13px;
            margin: 0;
            line-height: 1.5;
        }

        .settings-header a {
            color: var(--lavender-primary);
            text-decoration: none;
            font-weight: 500;
        }

        .api-key-input {
            position: relative;
            margin-bottom: 24px;
        }

        .api-key-input input {
            width: 100%;
            padding: 12px 40px 12px 16px;
            border: 2px solid var(--lavender-border);
            border-radius: 12px;
            font-size: 14px;
            font-family: 'Inter', sans-serif;
            background-color: white;
            transition: all 0.3s ease;
        }

        .api-key-input input:focus {
            border-color: var(--lavender-primary);
            box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
            outline: none;
        }

        .settings-footer {
            margin-top: 24px;
            padding-top: 20px;
            border-top: 1px solid var(--lavender-border);
            text-align: center;
        }

        .settings-footer p {
            color: var(--text-secondary);
            font-size: 12px;
            margin: 0 0 16px 0;
        }

        .dark-mode .settings-container {
            background: linear-gradient(145deg, #2d2d2d, #1a1a1a);
        }

        .dark-mode .api-key-input input {
            background-color: #2d2d2d;
            color: white;
            border-color: #4a4a4a;
        }

        .key-icon {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--lavender-primary);
            pointer-events: none;
        }

        /* Success notification animation */
        @keyframes slideDown {
            from { transform: translateY(-100%); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .notification {
            position: fixed;
            top: 16px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #10B981;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            gap: 8px;
            z-index: 1000;
            animation: slideDown 0.3s ease-out, fadeOut 0.3s ease-out 2s forwards;
        }

        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }

        .notification svg {
            width: 20px;
            height: 20px;
        }

        .model-selection-section {
            background-color: var(--lavender-light);
            padding: 20px;
            border-radius: 12px;
            margin: 24px 0;
            border: 1px solid var(--lavender-border);
        }

        .setting-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: var(--text-secondary);
            margin-bottom: 8px;
        }

        .model-select {
            width: 100%;
            padding: 12px;
            border: 2px solid var(--lavender-border);
            border-radius: 8px;
            font-size: 14px;
            font-family: 'Inter', sans-serif;
            background-color: white;
            transition: all 0.2s ease;
            box-sizing: border-box;
            cursor: pointer;
            margin-bottom: 12px;
        }

        .model-select:focus {
            outline: none;
            border-color: var(--lavender-primary);
            box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
        }

        .model-description {
            font-size: 12px;
            color: var(--text-secondary);
            line-height: 1.5;
            margin: 0;
        }

        .dark-mode .model-select {
            background-color: #2d2d2d;
            color: white;
            border-color: #4a4a4a;
        }

        .dark-mode .model-selection-section {
            background-color: rgba(124, 58, 237, 0.1);
        }
    </style>
</head>
<body>
    <div class="header">
        <img src="icon128.png" alt="Logo" class="logo">
        <h1>AI Summary</h1>
    </div>

    <div class="tabs">
        <div class="tab active" data-tab="summary">Summary</div>
        <div class="tab" data-tab="settings">Settings</div>
    </div>

    <div id="summary-tab" class="tab-content active">
        <button class="summarize-btn" id="summarize">
            <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
            Summarize Page
        </button>
        <div id="summary"></div>
        <div class="summary-actions" style="display: none">
            <div class="summary-stats">
                <span id="char-count">Characters: 0</span>
            </div>
            <div class="button-group">
                <button class="secondary-button" id="copy-summary">
                    <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"></path>
                    </svg>
                    Copy
                </button>
                <button class="secondary-button" id="download-summary">
                    <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                    </svg>
                    Download
                </button>
            </div>

        </div>
        <button id="save-summary" class="primary-button" style="margin-top: 16px; display: none;">
            Save Summary
        </button>
        <a href="saved.html" target="_blank" class="view-saved-link">View Saved Summaries</a>
    </div>

    <div class="tab-content" id="settings-tab">
        <div class="settings-container">
            <div class="settings-header">
                <h2>Configure Groq API</h2>
                <p>Get your API key from the <a href="https://console.groq.com/" target="_blank">Groq Console</a> to start summarizing content.</p>
            </div>

            <div class="api-key-input">
                <input
                    type="password"
                    id="groqApiKey"
                    placeholder="sk-xxxxxx..."
                    autocomplete="off"
                    spellcheck="false"
                >
                <div class="key-icon">
                    <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"/>
                    </svg>
                </div>
                <div class="messages-container"></div>
            </div>

            <div class="model-selection-section">
                <label for="modelSelect" class="setting-label">Model Selection</label>
                <select id="modelSelect" class="model-select">
                    <option value="llama-3.3-70b-versatile">Llama 3.3 70B Versatile</option>
                    <option value="meta-llama/llama-4-scout-17b-16e-instruct">Meta-Llama 4 Scout 17B 16E Instruct</option>
                </select>
                <p class="model-description">Choose the AI model that best suits your needs. Different models may offer varying levels of performance and capabilities.</p>
            </div>

            <div class="settings-footer">
                <p>Your API key is stored locally and never shared with third parties.</p>
                <div class="button-group">
                    <button class="primary-button" id="saveKey">
                        <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        Save API Key
                    </button>
                    <button class="secondary-button" id="clearKey">
                        <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                        Clear
                    </button>
                </div>
                <p class="copyright">
                    <a href="https://selfhood-studios.com" target="_blank" style="color: var(--text-secondary); text-decoration: none; font-size: 12px; margin-top: 16px; display: block;">
                        © Selfhood Studios, London
                    </a>
                </p>
            </div>
        </div>
    </div>

    <div class="theme-toggle">
        <label class="switch">
            <input type="checkbox" id="theme-switch">
            <span class="slider round"></span>
        </label>
        <span>Dark Mode</span>
    </div>

    <!-- Add this before </body> -->
    <script src="popup.js"></script>
</body>
</html>
