document.addEventListener('DOMContentLoaded', () => {
    // Tab switching logic with error handling
    const tabs = document.querySelectorAll('.tab');
    const tabContents = document.querySelectorAll('.tab-content');

    if (tabs.length === 0 || tabContents.length === 0) {
        console.error('Could not find tab elements');
        return;
    }

    // Initialize elements
    const summarizeBtn = document.getElementById('summarize');
    const summaryDiv = document.getElementById('summary');
    const saveKeyBtn = document.getElementById('saveKey');
    const clearKeyBtn = document.getElementById('clearKey');
    const saveSummaryBtn = document.getElementById('save-summary');
    const copyBtn = document.getElementById('copy-summary');
    const downloadBtn = document.getElementById('download-summary');
    const themeSwitch = document.getElementById('theme-switch');
    const summaryActions = document.querySelector('.summary-actions');

    const groqApiKeyInput = document.getElementById('groqApiKey');
    const modelSelect = document.getElementById('modelSelect');

    // Update initialization checks to include all required elements and model select
    if (!summarizeBtn || !summaryDiv || !saveKeyBtn || !clearKeyBtn || !groqApiKeyInput || !modelSelect) {
        console.error('Required elements not found:', {
            summarizeBtn: !!summarizeBtn,
            summaryDiv: !!summaryDiv,
            saveKeyBtn: !!saveKeyBtn,
            clearKeyBtn: !!clearKeyBtn,
            groqApiKeyInput: !!groqApiKeyInput,
            modelSelect: !!modelSelect
        });
        return;
    }

    // Load saved API key and model on startup
    chrome.storage.sync.get(['groqApiKey', 'selectedModel'], (data) => {
        if (data.groqApiKey) {
            groqApiKeyInput.value = data.groqApiKey;
        }
        if (data.selectedModel) {
            modelSelect.value = data.selectedModel;
        }
    });

    // Improved tab switching
    tabs.forEach(tab => {
        tab.addEventListener('click', () => {
            // Remove active class from all tabs and contents
            tabs.forEach(t => t.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // Add active class to clicked tab
            tab.classList.add('active');

            // Show corresponding content
            const tabId = `${tab.dataset.tab}-tab`;
            const content = document.getElementById(tabId);
            if (content) {
                content.classList.add('active');
                console.log(`Switched to tab: ${tabId}`);
            } else {
                console.error(`Could not find tab content with id: ${tabId}`);
            }
        });
    });

    const settingsTab = document.querySelector('.tab[data-tab="settings"]');
    const settingsContent = document.getElementById('settings-tab');

    if (settingsTab && settingsContent) {
        settingsTab.addEventListener('click', () => {
            tabs.forEach(t => t.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            settingsTab.classList.add('active');
            settingsContent.classList.add('active');
        });
    } else {
        console.error('Settings tab or content not found');
    }

    // Improved save API key handler
    saveKeyBtn.addEventListener('click', async () => {
        const groqApiKey = groqApiKeyInput?.value.trim();

        if (!groqApiKey) {
            showMessage('Please enter an API key', 'error');
            return;
        }

        try {
            // Validate API key format
            if (!groqApiKey.startsWith('gsk_')) {
                throw new Error('Invalid Groq API key format. Key should start with "gsk_"');
            }

            await chrome.storage.sync.set({ groqApiKey });
            showMessage('API key saved successfully!', 'success');
        } catch (error) {
            showMessage(error.message, 'error');
        }
    });

    // Improved clear API key handler
    clearKeyBtn.addEventListener('click', async () => {
        try {
            groqApiKeyInput.value = '';
            await chrome.storage.sync.remove('groqApiKey');
            showMessage('API key cleared successfully', 'success');
        } catch (error) {
            showMessage('Error clearing API key', 'error');
        }
    });

    // Theme toggle
    themeSwitch.addEventListener('change', () => {
        document.body.classList.toggle('dark-mode');
        chrome.storage.sync.set({ darkMode: themeSwitch.checked });
    });

    // Load theme preference
    chrome.storage.sync.get('darkMode', (data) => {
        if (data.darkMode) {
            themeSwitch.checked = true;
            document.body.classList.add('dark-mode');
        }
    });

    // Save model selection when changed
    modelSelect.addEventListener('change', async () => {
        try {
            await chrome.storage.sync.set({ selectedModel: modelSelect.value });
            showMessage('Model preference saved!', 'success');
        } catch (error) {
            showMessage('Error saving model preference', 'error');
        }
    });

    // Summarize button - simplified for Groq
    summarizeBtn.addEventListener('click', async () => {
        chrome.storage.sync.get(['groqApiKey', 'selectedModel'], async (data) => {
            if (!data.groqApiKey) {
                summaryDiv.innerHTML = '<div class="error-message">Please enter your Groq API key in settings</div>';
                return;
            }

            summaryDiv.innerHTML = '<div class="loading"><div class="spinner"></div><span>Processing text and generating summary...</span></div>';
            saveSummaryBtn.style.display = 'none';

            try {
                const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
                const [{ result }] = await chrome.scripting.executeScript({
                    target: { tabId: tab.id },
                    function: () => {
                        /**
                         * Optimized content extraction algorithm for large pages
                         * - Uses more efficient selectors and processing
                         * - Implements chunking for large content
                         * - Adds performance optimizations for DOM traversal
                         */

                        // Performance optimization: Create a document fragment to avoid reflows
                        const createWorkingCopy = (element) => {
                            return element.cloneNode(true);
                        };

                        // Helper function to clean text efficiently
                        const cleanText = (text) => {
                            return text.replace(/\s+/g, ' ').trim();
                        };

                        // Helper to check if element is likely to be content
                        const isLikelyContent = (element) => {
                            // Skip invisible elements
                            if (element.offsetParent === null &&
                                !['BODY', 'HTML'].includes(element.tagName)) {
                                return false;
                            }

                            const style = window.getComputedStyle(element);
                            if (style.display === 'none' || style.visibility === 'hidden') {
                                return false;
                            }

                            // Check tag name
                            const tagName = element.tagName.toLowerCase();
                            if (['nav', 'header', 'footer', 'aside', 'script', 'style', 'noscript',
                                 'iframe', 'form', 'svg', 'path'].includes(tagName)) {
                                return false;
                            }

                            // Check class and ID for common non-content patterns
                            const classAndId = (element.className + ' ' + element.id).toLowerCase();
                            const unwantedPatterns = [
                                /(nav|menu|header|footer|sidebar|comment|ad|widget|related|social|share|author|timestamp|caption|search|recommended|popular|trending)/i,
                                /(cookie|banner|modal|popup|overlay|dialog|notification|alert|tooltip|navbar|topbar|toolbar|bottom-bar)/i
                            ];

                            if (unwantedPatterns.some(pattern => pattern.test(classAndId))) {
                                return false;
                            }

                            return true;
                        };

                        // Try high-priority selectors first (most likely to contain the main content)
                        const highPrioritySelectors = [
                            'article',
                            'main',
                            '[role="main"]',
                            '.article',
                            '.post-content',
                            '.entry-content',
                            '[itemprop="articleBody"]',
                            '.article-content',
                            '.article__content',
                            '.article-body'
                        ];

                        // Try to find content using high-priority selectors first
                        let mainContent = null;
                        let mainContentScore = 0;

                        // Use a more efficient selector approach
                        for (const selector of highPrioritySelectors) {
                            // Use querySelectorAll and check only the first few elements for better performance
                            const elements = document.querySelectorAll(selector);
                            const maxElementsToCheck = Math.min(elements.length, 5); // Check at most 5 elements

                            for (let i = 0; i < maxElementsToCheck; i++) {
                                const element = elements[i];
                                if (element && isLikelyContent(element)) {
                                    // Quick length estimation instead of full text extraction
                                    const textLength = element.innerText?.length || 0;
                                    const paragraphs = element.querySelectorAll('p').length;

                                    // Score based on text length and paragraph count
                                    const score = textLength + (paragraphs * 100);

                                    if (score > mainContentScore) {
                                        mainContent = element;
                                        mainContentScore = score;
                                    }
                                }
                            }

                            // If we found a good match, stop searching
                            if (mainContentScore > 2000) {
                                break;
                            }
                        }

                        // If no good match found with high-priority selectors, try a more comprehensive approach
                        if (!mainContent || mainContentScore < 1000) {
                            // Use a more targeted approach for large pages
                            // Instead of querying all divs (which could be thousands), focus on potential content areas
                            const potentialContentSelectors = [
                                'div > p', // Divs that contain paragraphs
                                'section',
                                'div.content',
                                'div[class*="content"]',
                                'div[class*="article"]',
                                'div[class*="post"]',
                                'div[class*="entry"]',
                                'div[class*="blog"]',
                                'div[class*="text"]',
                                'div[class*="body"]'
                            ];

                            // Collect potential content blocks
                            const contentBlocks = new Set();

                            for (const selector of potentialContentSelectors) {
                                const elements = document.querySelectorAll(selector);
                                // Limit the number of elements to check for performance
                                const maxElementsToCheck = Math.min(elements.length, 20);

                                for (let i = 0; i < maxElementsToCheck; i++) {
                                    const element = elements[i];
                                    // For paragraph selectors, get the parent
                                    const targetElement = selector === 'div > p' ? element.parentElement : element;

                                    if (targetElement && isLikelyContent(targetElement)) {
                                        contentBlocks.add(targetElement);
                                    }
                                }

                                // If we've found enough potential blocks, stop searching
                                if (contentBlocks.size >= 20) {
                                    break;
                                }
                            }

                            // Convert to array and filter for content quality
                            const filteredBlocks = Array.from(contentBlocks).filter(el => {
                                // Quick check for meaningful content
                                const textLength = el.innerText?.length || 0;
                                if (textLength < 200) return false;

                                // Check for paragraph density
                                const paragraphs = el.querySelectorAll('p').length;
                                if (paragraphs < 2 && textLength < 500) return false;

                                // Check link density (avoid navigation)
                                const links = el.querySelectorAll('a');
                                if (links.length > 0) {
                                    const linkTextLength = Array.from(links).reduce(
                                        (sum, link) => sum + (link.innerText?.length || 0), 0
                                    );
                                    const linkDensity = linkTextLength / textLength;
                                    if (linkDensity > 0.3) return false;
                                }

                                return true;
                            });

                            // Find the best content block
                            if (filteredBlocks.length > 0) {
                                mainContent = filteredBlocks.reduce((best, current) => {
                                    // Use innerText instead of textContent for better performance on large nodes
                                    const currentText = current.innerText || '';
                                    const bestText = best.innerText || '';

                                    const currentParagraphs = current.querySelectorAll('p').length;
                                    const bestParagraphs = best.querySelectorAll('p').length;

                                    // Calculate scores - prioritize paragraph count more for better content identification
                                    const currentScore = currentText.length + (currentParagraphs * 150);
                                    const bestScore = bestText.length + (bestParagraphs * 150);

                                    return currentScore > bestScore ? current : best;
                                }, filteredBlocks[0]);
                            }
                        }

                        // Process the content
                        if (mainContent) {
                            // Create a working copy to avoid modifying the original DOM
                            const workingCopy = createWorkingCopy(mainContent);

                            // Remove unwanted elements
                            const elementsToRemove = workingCopy.querySelectorAll(
                                'script, style, noscript, iframe, nav, form, aside, ' +
                                'svg, button, input, .ad, .ads, .advertisement, ' +
                                '[class*="cookie"], [class*="popup"], [class*="banner"]'
                            );

                            elementsToRemove.forEach(el => el.remove());

                            // For very large content, extract only the most relevant parts
                            const maxContentLength = 100000; // Limit content length for processing efficiency
                            let extractedText = workingCopy.innerText || '';

                            if (extractedText.length > maxContentLength) {
                                // For very large content, take the beginning and important parts
                                const firstPart = extractedText.substring(0, maxContentLength * 0.6);

                                // Try to find important parts in the middle (paragraphs with keywords)
                                const paragraphs = workingCopy.querySelectorAll('p');
                                let importantMiddleParts = '';

                                // Keywords that might indicate important content
                                const importantKeywords = [
                                    'conclusion', 'summary', 'result', 'finding', 'important',
                                    'significant', 'key', 'main', 'critical', 'essential'
                                ];

                                // Check a limited number of paragraphs
                                const maxParagraphsToCheck = Math.min(paragraphs.length, 50);
                                for (let i = 0; i < maxParagraphsToCheck; i++) {
                                    const paragraphText = paragraphs[i].innerText || '';
                                    if (paragraphText.length > 100 &&
                                        importantKeywords.some(keyword =>
                                            paragraphText.toLowerCase().includes(keyword))) {
                                        importantMiddleParts += paragraphText + ' ';

                                        // Limit the important parts length
                                        if (importantMiddleParts.length > maxContentLength * 0.3) {
                                            break;
                                        }
                                    }
                                }

                                // Combine first part with important parts
                                extractedText = firstPart + ' ' + importantMiddleParts;
                            }

                            return cleanText(extractedText);
                        } else {
                            // Fallback: If no main content found, extract from body more efficiently
                            const body = document.body;

                            // Instead of cloning the entire body (which can be expensive),
                            // directly extract text from main content areas
                            const contentAreas = [];

                            // Get text from all paragraphs (usually the most relevant content)
                            const paragraphs = body.querySelectorAll('p');
                            paragraphs.forEach(p => {
                                if (p.innerText?.length > 50 && isLikelyContent(p)) {
                                    contentAreas.push(p.innerText);
                                }
                            });

                            // If we found enough paragraph content, use that
                            if (contentAreas.join(' ').length > 500) {
                                return cleanText(contentAreas.join(' '));
                            }

                            // Otherwise, fall back to a more aggressive approach
                            // But limit the content length for very large pages
                            let bodyText = body.innerText || '';
                            if (bodyText.length > 150000) {
                                bodyText = bodyText.substring(0, 150000);
                            }

                            return cleanText(bodyText);
                        }
                    }
                });

                // Add CSS for the UI elements if not already present
                if (!document.getElementById('progressive-loading-styles')) {
                    const style = document.createElement('style');
                    style.id = 'progressive-loading-styles';
                    style.textContent = `
                        .partial-summary {
                            opacity: 0.9;
                            margin-bottom: 10px;
                            font-family: 'Nebula Sans', 'Inter', sans-serif;
                        }
                        .loading-more {
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            padding: 10px;
                            background: rgba(124, 58, 237, 0.05);
                            border-radius: 8px;
                            margin-top: 10px;
                            font-family: 'Nebula Sans', 'Inter', sans-serif;
                        }
                        .small-spinner {
                            width: 16px;
                            height: 16px;
                            margin-right: 8px;
                        }
                        .fallback-notice {
                            margin-top: 8px;
                            padding: 6px 10px;
                            background: rgba(255, 193, 7, 0.1);
                            border-left: 3px solid #ffc107;
                            font-size: 12px;
                            color: #856404;
                            border-radius: 4px;
                            font-family: 'Nebula Sans', 'Inter', sans-serif;
                            letter-spacing: -0.01em;
                        }
                    `;
                    document.head.appendChild(style);
                }

                // Initial loading state
                summaryDiv.innerHTML = `<div class="loading">
                    <div class="spinner"></div>
                    <span>Starting summarization process...</span>
                </div>`;

                // Start the summarization process
                chrome.runtime.sendMessage({
                    type: 'summarize',
                    content: result,
                    apiKey: data.groqApiKey,
                    model: data.selectedModel || 'llama-3.3-70b-versatile' // Default model if none selected
                }, response => {
                    if (response.taskId) {
                        // We got a task ID, start polling for updates
                        pollForSummaryUpdates(response.taskId);
                    } else if (response.error) {
                        // Show error message
                        summaryDiv.innerHTML = `<div class="error-message">${response.error}</div>`;
                        summaryActions.style.display = 'none';
                    } else {
                        // Fallback for unexpected response format
                        summaryDiv.innerHTML = `<div class="error-message">Unexpected response from server</div>`;
                        summaryActions.style.display = 'none';
                    }
                });

                /**
                 * Poll for updates on the summarization task
                 * @param {string} taskId - The ID of the task to check
                 */
                function pollForSummaryUpdates(taskId) {
                    // Set up polling interval
                    const pollInterval = setInterval(() => {
                        chrome.runtime.sendMessage({
                            type: 'checkStatus',
                            taskId: taskId
                        }, response => {
                            if (!response) {
                                clearInterval(pollInterval);
                                summaryDiv.innerHTML = `<div class="error-message">Lost connection to the background process</div>`;
                                summaryActions.style.display = 'none';
                                return;
                            }

                            // Handle different task statuses
                            switch (response.status) {
                                case 'starting':
                                    summaryDiv.innerHTML = `<div class="loading">
                                        <div class="spinner"></div>
                                        <span>Starting summarization process...</span>
                                    </div>`;
                                    break;

                                case 'processing':
                                    summaryDiv.innerHTML = `<div class="loading">
                                        <div class="spinner"></div>
                                        <span>${response.message || 'Processing text...'}</span>
                                    </div>`;
                                    break;

                                case 'summarizing':
                                    summaryDiv.innerHTML = `<div class="loading">
                                        <div class="spinner"></div>
                                        <span>${response.message || 'Generating summary...'}</span>
                                    </div>`;
                                    break;

                                case 'fallback':
                                    summaryDiv.innerHTML = `<div class="loading">
                                        <div class="spinner"></div>
                                        <span>${response.message || 'Using direct summarization...'}</span>
                                        <div class="fallback-notice">API processing failed. Using direct summarization.</div>
                                    </div>`;
                                    break;

                                case 'complete':
                                    clearInterval(pollInterval); // Stop polling

                                    // For long summaries, implement progressive loading
                                    if (response.isLong && response.partialSummary) {
                                        // First show partial summary with loading indicator
                                        summaryDiv.innerHTML = `
                                            <div class="partial-summary">${response.partialSummary}</div>
                                            <div class="loading-more">
                                                <div class="spinner small-spinner"></div>
                                                <span>Loading full summary...</span>
                                            </div>`;

                                        // After a short delay, show the full summary
                                        setTimeout(() => {
                                            displayCompleteSummary(response);
                                        }, 800);
                                    } else {
                                        // Directly show the complete summary
                                        displayCompleteSummary(response);
                                    }
                                    break;

                                case 'error':
                                    clearInterval(pollInterval); // Stop polling
                                    summaryDiv.innerHTML = `<div class="error-message">${response.error || 'An error occurred during summarization'}</div>`;
                                    summaryActions.style.display = 'none';
                                    break;

                                default:
                                    // Unknown status
                                    summaryDiv.innerHTML = `<div class="loading">
                                        <div class="spinner"></div>
                                        <span>Processing request...</span>
                                    </div>`;
                            }
                        });
                    }, 1000); // Check every second

                    // Safety timeout to prevent infinite polling
                    setTimeout(() => {
                        clearInterval(pollInterval);
                    }, 300000); // 5 minutes max
                }

                /**
                 * Display the complete summary with appropriate UI elements
                 * @param {Object} response - The task response object
                 */
                function displayCompleteSummary(response) {
                    let summaryContent = response.summary;

                    // If this was a fallback, add a notice
                    if (response.fallback) {
                        summaryContent = `
                            <div class="fallback-notice">
                                Note: This summary was generated without API text processing due to connection issues.
                            </div>
                            ${summaryContent}`;
                    }

                    summaryDiv.innerHTML = summaryContent;
                    saveSummaryBtn.style.display = 'block';
                    summaryActions.style.display = 'block';

                    const charCount = document.getElementById('char-count');
                    if (charCount) {
                        charCount.textContent = `Characters: ${response.summary.length}`;
                    }
                }
            } catch (error) {
                summaryDiv.innerHTML = `<div class="error-message">Error: ${error.message}</div>`;
                summaryActions.style.display = 'none';
            }
        });
    });

    // Update the save summary button event listener
    saveSummaryBtn.addEventListener('click', async () => {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        const summary = summaryDiv.textContent;

        chrome.storage.sync.get('savedSummaries', (data) => {
            const savedSummaries = data.savedSummaries || [];
            savedSummaries.push({
                url: tab.url,
                summary: summary,
                date: new Date().toISOString()
            });
            chrome.storage.sync.set({ savedSummaries }, () => {
                // Create and show notification
                const notification = document.createElement('div');
                notification.className = 'notification';
                notification.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                    Summary saved successfully!
                `;
                document.body.appendChild(notification);

                // Remove notification after animation
                setTimeout(() => {
                    notification.remove();
                }, 2300);
            });
        });
    });

    // Copy to clipboard
    copyBtn.addEventListener('click', () => {
        navigator.clipboard.writeText(summaryDiv.textContent)
            .then(() => showMessage('Summary copied to clipboard!', 'success'))
            .catch(() => showMessage('Failed to copy text', 'error'));
    });

    // Download summary
    downloadBtn.addEventListener('click', () => {
        const text = summaryDiv.textContent;
        const blob = new Blob([text], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'summary.txt';
        a.click();
        URL.revokeObjectURL(url);
    });


});

// Helper function to show messages
function showMessage(message, type) {
    const messageDiv = document.createElement('div');
    messageDiv.className = type === 'error' ? 'error-message' : 'success-message';
    messageDiv.textContent = message;

    const existingMessages = document.querySelectorAll('.error-message, .success-message');
    existingMessages.forEach(msg => msg.remove());

    const settingsContainer = document.querySelector('.settings-container');
    if (settingsContainer) {
        settingsContainer.appendChild(messageDiv);
    } else {
        document.querySelector('.api-key-section').appendChild(messageDiv);
    }

    setTimeout(() => messageDiv.remove(), 3000);
}

// Update or add this function
async function summarizeWithGroq(text, apiKey) {
    const endpoint = 'https://api.groq.com/v1/chat/completions';
    try {
        const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${apiKey.trim()}`, // Ensure key is trimmed
                'Content-Type': 'application/json',
                'Accept': 'application/json'  // Add explicit Accept header
            },
            body: JSON.stringify({
                model: "meta-llama/llama-4-scout-17b-16e-instruct",
                messages: [
                    {
                        role: "system",
                        content: "You are a helpful assistant that generates concise summaries."
                    },
                    {
                        role: "user",
                        content: `Please summarize the following text:\n\n${text}`
                    }
                ],
                temperature: 0.7,
                max_tokens: 32768
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            if (response.status === 401) {
                throw new Error('Invalid API key. Please check your Groq API key in settings.');
            } else {
                throw new Error(errorData.error?.message || `Groq API error: ${response.status}`);
            }
        }

        const data = await response.json();
        return data.choices[0].message.content;
    } catch (error) {
        if (error.message.includes('Failed to fetch')) {
            throw new Error('Failed to connect to Groq API. Please check your internet connection.');
        }
        throw error;
    }
}

// Update your main summarize function to handle Groq properly
async function summarize() {
    // ...existing code...
    try {
        // Variable will be used when implementing full provider functionality
        // let summary;
        const provider = await getStoredProvider();
        if (provider === 'groq') {
            const apiKey = await getStoredApiKey('groqApiKey');
            if (!apiKey) {
                throw new Error('Groq API key not found. Please add it in the settings.');
            }
            summary = await summarizeWithGroq(text, apiKey);
        }
        // ...rest of the code...
    } catch (error) {
        showError(error.message);
    }
}

// ...existing code...