{"manifest_version": 3, "name": "<PERSON> Summarizer", "version": "1.3", "description": "Summarizes the content of the current page using advanced text processing and Groq AI.", "permissions": ["storage", "activeTab", "scripting"], "host_permissions": ["https://api.groq.com/*", "http://**************:8081/*"], "action": {"default_popup": "popup.html", "default_icon": {"128": "icon128.png"}}, "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["<all_urls>"], "exclude_matches": ["*://*.facebook.com/*", "*://*.twitter.com/*", "*://*.x.com/*", "*://*.instagram.com/*", "*://*.linkedin.com/*", "*://*.tiktok.com/*", "*://*.google.com/*", "*://*.google.com/search*", "*://*.bing.com/*", "*://*.yahoo.com/*", "*://*.duckduckgo.com/*", "*://*.reddit.com/*", "*://*.youtube.com/*", "*://*.pinterest.com/*", "*://*.snapchat.com/*", "*://*.whatsapp.com/*", "*://*.telegram.org/*", "*://*.discord.com/*", "*://*.slack.com/*", "*://*.messenger.com/*"], "js": ["content.js"]}], "web_accessible_resources": [{"resources": ["saved.html"], "matches": ["<all_urls>"]}]}