chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.summary) {
        // Remove existing summary if present
        const existingSummary = document.getElementById('groq-summary');
        if (existingSummary) {
            existingSummary.remove();
        }

        // Create and style the summary div
        const summaryDiv = document.createElement('div');
        summaryDiv.id = 'groq-summary';
        Object.assign(summaryDiv.style, {
            position: 'fixed',
            bottom: '20px',
            right: '20px',
            width: '300px',
            backgroundColor: 'white',
            border: '1px solid #ccc',
            borderRadius: '8px',
            padding: '15px',
            zIndex: '10000',
            maxHeight: '300px',
            overflowY: 'auto',
            overflowX: 'hidden',
            boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
            fontSize: '14px',
            lineHeight: '1.5'
        });

        // Add close button
        const closeButton = document.createElement('button');
        Object.assign(closeButton.style, {
            position: 'absolute',
            right: '5px',
            top: '5px',
            border: 'none',
            background: 'none',
            cursor: 'pointer',
            fontSize: '16px'
        });
        closeButton.innerHTML = '✕';
        closeButton.onclick = () => summaryDiv.remove();

        summaryDiv.appendChild(closeButton);

        // Add summary text
        const textDiv = document.createElement('div');
        textDiv.style.marginTop = '10px';
        textDiv.textContent = request.summary;
        summaryDiv.appendChild(textDiv);

        document.body.appendChild(summaryDiv);
    }
});